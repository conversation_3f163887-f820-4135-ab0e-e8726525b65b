module.exports = {
  plugins: [
    require('postcss-import')({
      path: ['src/styles']
    }),
    require('postcss-custom-properties')({
      preserve: true
    }),
    require('autoprefixer')({
      overrideBrowserslist: [
        'last 2 Chrome versions',
        'last 2 Firefox versions',
        'last 2 Safari versions',
        'last 2 Edge versions'
      ]
    }),
    ...(process.env.NODE_ENV === 'production' ? [
      require('cssnano')({
        preset: ['default', {
          discardComments: {
            removeAll: true
          },
          normalizeWhitespace: true,
          colormin: true,
          convertValues: true,
          calc: true,
          minifyFontValues: true,
          minifyGradients: true,
          minifyParams: true,
          minifySelectors: true,
          minifyUrls: true,
          mergeLonghand: true,
          mergeRules: true,
          normalizePositions: true,
          normalizeRepeatStyle: true,
          normalizeUrl: true,
          reduceTransforms: true,
          svgo: true
        }]
      })
    ] : [])
  ]
};