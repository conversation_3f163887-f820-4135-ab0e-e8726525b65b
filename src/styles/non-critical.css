/* Non-Critical CSS - Below-the-fold and enhancement styles */
/* This file contains styles that can be loaded asynchronously after initial render */

/* Import component styles */
@import './components/index.css';

/* Scrollbar Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar-thumb) var(--color-scrollbar-track);
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

*::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar-thumb);
  border-radius: var(--radius-full);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-scrollbar-thumb-hover);
}

/* Enhanced Button Styles */
button:hover {
  background-color: var(--color-bg-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-button-hover);
}

button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-button-active);
}

button.primary {
  background-color: var(--color-button-primary-bg);
  color: var(--color-button-primary-text);
  border-color: var(--color-button-primary-bg);
}

button.primary:hover {
  background-color: var(--color-button-primary-hover);
  border-color: var(--color-button-primary-hover);
}

button.primary:active {
  background-color: var(--color-button-primary-active);
  border-color: var(--color-button-primary-active);
}

/* Enhanced Input Styles */
input[type="text"]:focus,
input[type="search"]:focus {
  border-color: var(--color-border-focus);
  box-shadow: var(--shadow-input-focus);
}

input[type="text"]::placeholder,
input[type="search"]::placeholder {
  color: var(--color-text-placeholder);
  font-weight: var(--input-placeholder-weight);
}

/* Textarea Styles */
textarea {
  padding: var(--input-padding-y) var(--input-padding-x);
  border: var(--input-border);
  border-radius: var(--input-border-radius);
  font-family: inherit;
  font-size: var(--input-text-size);
  outline: none;
  width: 100%;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: var(--transition-input);
  resize: vertical;
  min-height: 100px;
  line-height: var(--line-height-normal);
}

textarea:focus {
  border-color: var(--color-border-focus);
  box-shadow: var(--shadow-input-focus);
}

/* Select Styles */
select {
  padding: var(--input-padding-y) var(--input-padding-x);
  border: var(--input-border);
  border-radius: var(--input-border-radius);
  font-family: inherit;
  font-size: var(--input-text-size);
  outline: none;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-input);
}

select:focus {
  border-color: var(--color-border-focus);
  box-shadow: var(--shadow-input-focus);
}

/* Checkbox and Radio Styles */
input[type="checkbox"],
input[type="radio"] {
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
  accent-color: var(--color-brand-primary);
}

/* Label Styles */
label {
  display: block;
  margin-bottom: var(--input-label-gap);
  font-size: var(--input-label-size);
  font-weight: var(--input-label-weight);
  color: var(--color-text-secondary);
}

/* Link Styles */
a {
  color: var(--color-text-link);
  text-decoration: none;
  transition: var(--transition-colors);
}

a:hover {
  color: var(--color-text-link-hover);
  text-decoration: underline;
}

a:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
  border-radius: var(--radius-xs);
}

/* Code and Pre Styles */
code {
  font-family: var(--code-family);
  font-size: var(--code-size);
  background-color: var(--color-bg-tertiary);
  padding: 2px 4px;
  border-radius: var(--radius-xs);
  color: var(--color-text-primary);
}

pre {
  font-family: var(--code-family);
  font-size: var(--code-size);
  line-height: var(--code-line-height);
  background-color: var(--color-bg-tertiary);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  overflow-x: auto;
  white-space: pre;
}

pre code {
  background-color: transparent;
  padding: 0;
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-base);
}

thead {
  background-color: var(--color-bg-secondary);
  border-bottom: var(--table-border-header);
}

th {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

td {
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: var(--table-border-cell);
  color: var(--color-text-secondary);
}

tbody tr:hover {
  background-color: var(--color-bg-hover);
}

/* List Styles */
ul, ol {
  margin: 0;
  padding-left: var(--spacing-6);
  line-height: var(--line-height-relaxed);
}

li {
  margin-bottom: var(--spacing-1);
}

/* Horizontal Rule */
hr {
  border: none;
  border-top: var(--divider-horizontal);
  margin: var(--spacing-6) 0;
}

/* Blockquote */
blockquote {
  margin: var(--spacing-4) 0;
  padding: var(--spacing-3) var(--spacing-4);
  border-left: 4px solid var(--color-brand-primary);
  background-color: var(--color-bg-tertiary);
  font-style: italic;
  color: var(--color-text-secondary);
}

/* Utility Classes */
.monospace {
  font-family: var(--font-family-mono);
  line-height: var(--line-height-normal);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-disabled {
  color: var(--color-text-disabled);
}

.text-error {
  color: var(--color-error);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-small {
  font-size: var(--font-size-sm);
}

.text-large {
  font-size: var(--font-size-lg);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-light {
  font-weight: var(--font-weight-light);
}

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-1); }
.pt-2 { padding-top: var(--spacing-2); }
.pt-3 { padding-top: var(--spacing-3); }
.pt-4 { padding-top: var(--spacing-4); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-1); }
.pb-2 { padding-bottom: var(--spacing-2); }
.pb-3 { padding-bottom: var(--spacing-3); }
.pb-4 { padding-bottom: var(--spacing-4); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--spacing-1); }
.pl-2 { padding-left: var(--spacing-2); }
.pl-3 { padding-left: var(--spacing-3); }
.pl-4 { padding-left: var(--spacing-4); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--spacing-1); }
.pr-2 { padding-right: var(--spacing-2); }
.pr-3 { padding-right: var(--spacing-3); }
.pr-4 { padding-right: var(--spacing-4); }

/* Display Utilities */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.grid {
  display: grid;
}

/* Flexbox Utilities */
.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-column {
  flex-direction: column;
}

.flex-column-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.gap-1 {
  gap: var(--gap-sm);
}

.gap-2 {
  gap: var(--gap-md);
}

.gap-3 {
  gap: var(--gap-lg);
}

.gap-4 {
  gap: var(--gap-xl);
}

/* Position Utilities */
.static {
  position: static;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* Animation Classes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-fadeIn {
  animation: fadeIn var(--animation-duration-fade-in) var(--ease-decelerate);
}

.animate-fadeOut {
  animation: fadeOut var(--animation-duration-fade-out) var(--ease-accelerate);
}

.animate-slideInUp {
  animation: slideInUp var(--animation-duration-slide-in) var(--ease-decelerate);
}

.animate-slideInDown {
  animation: slideInDown var(--animation-duration-slide-in) var(--ease-decelerate);
}

.animate-scaleIn {
  animation: scaleIn var(--duration-base) var(--ease-bounce);
}

.animate-pulse {
  animation: pulse var(--animation-duration-pulse) var(--ease-in-out) infinite;
}