/* Critical CSS - Above-the-fold styles for immediate render */
/* This file contains only essential styles needed for initial page load */

/* Import design tokens - critical for all styles */
@import './tokens/index.css';

/* Critical Resets */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

/* Critical Base Styles */
html {
  height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-primary, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif);
  margin: 0;
  padding: 0;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  height: 100vh;
  overflow: hidden;
  min-height: 100vh;
  position: relative;
}

/* Critical Layout Structure */
#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: var(--color-bg-primary);
}

/* Critical Header Styles */
.header {
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: var(--border-default);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--color-bg-primary);
  min-height: var(--layout-header-height);
  flex-shrink: 0;
}

.header h1 {
  font-size: var(--heading-4-size);
  font-weight: var(--heading-4-weight);
  margin: 0;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
}

/* Critical Main Content Layout */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: 100%;
  flex-direction: row-reverse;
  position: relative;
}

/* Critical Sidebar Layout */
.sidebar {
  width: var(--layout-sidebar-width);
  height: 100%;
  display: flex;
  flex-direction: column;
  border-left: var(--border-default);
  overflow: hidden;
  background-color: var(--color-bg-secondary);
  position: relative;
  flex-shrink: 0;
}

/* Critical Content Area */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--color-bg-primary);
  position: relative;
}

/* Critical Loading States */
.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
}

.loading-spinner::after {
  content: '';
  display: block;
  width: 32px;
  height: 32px;
  margin: 4px;
  border-radius: 50%;
  border: 3px solid var(--color-brand-primary);
  border-color: var(--color-brand-primary) transparent var(--color-brand-primary) transparent;
  animation: loading-spin 1.2s linear infinite;
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Critical Error States */
.error-message {
  padding: var(--spacing-4);
  background-color: var(--color-error-alpha-20);
  border: 1px solid var(--color-error);
  border-radius: var(--radius-md);
  color: var(--color-error);
  margin: var(--spacing-4);
  font-size: var(--font-size-base);
}

/* Critical Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

p {
  margin: 0;
  line-height: var(--line-height-normal);
}

/* Critical Button Base */
button {
  cursor: pointer;
  font-family: inherit;
  border: var(--border-default);
  background-color: var(--color-bg-primary);
  font-size: var(--button-text-size-md);
  font-weight: var(--button-text-weight);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--button-icon-gap);
  padding: var(--button-padding-y-md) var(--button-padding-x-md);
  min-height: 32px;
  border-radius: var(--radius-button);
  transition: var(--transition-button);
  white-space: nowrap;
  color: var(--color-text-primary);
  position: relative;
  outline: none;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Critical Input Base */
input[type="text"],
input[type="search"] {
  padding: var(--input-padding-y) var(--input-padding-x);
  border: var(--input-border);
  border-radius: var(--input-border-radius);
  font-family: inherit;
  font-size: var(--input-text-size);
  outline: none;
  width: 100%;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: var(--transition-input);
}

/* Critical Focus States */
:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

button:focus-visible,
input:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

/* Critical Dark Mode Support */
.dark-mode {
  color-scheme: dark;
}

/* Critical Accessibility */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Skip to Content Link */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--color-bg-elevated);
  color: var(--color-text-primary);
  padding: var(--spacing-2) var(--spacing-4);
  z-index: 100;
  text-decoration: none;
  border-radius: var(--radius-md);
}

.skip-to-content:focus {
  top: 0;
}

/* Critical Layout Utilities */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.overflow-hidden {
  overflow: hidden;
}

/* Critical Z-index Layers */
:root {
  --z-index-dropdown: 50;
  --z-index-sticky: 100;
  --z-index-fixed: 200;
  --z-index-modal-backdrop: 300;
  --z-index-modal: 400;
  --z-index-popover: 500;
  --z-index-tooltip: 600;
  --z-index-toast: 700;
}