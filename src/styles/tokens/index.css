/* Design Token System - Central Import File */
/* This file aggregates all design tokens for the application */

/* Import all token categories in order of dependency */
@import './colors.css';
@import './spacing.css';
@import './typography.css';
@import './borders.css';
@import './shadows.css';
@import './transitions.css';

/* 
  Token System Documentation:
  
  1. Colors (colors.css)
     - Brand colors with variants
     - Semantic colors (success, warning, error, info)
     - Neutral color scale
     - Component-specific colors
     - Dark mode support
  
  2. Spacing (spacing.css)
     - 8px grid system
     - Semantic spacing scales (xs, sm, md, lg, xl)
     - Component-specific spacing
     - Layout dimensions
  
  3. Typography (typography.css)
     - Font families, sizes, and weights
     - Line heights and letter spacing
     - Component typography presets
     - Heading and body text styles
  
  4. Borders (borders.css)
     - Border widths and styles
     - Border radius scale
     - Focus rings and outlines
     - Component border presets
  
  5. Shadows (shadows.css)
     - Elevation system (0-5 levels)
     - Directional and inset shadows
     - Component shadow presets
     - Glass morphism effects
  
  6. Transitions (transitions.css)
     - Duration scale
     - Easing functions
     - Property-specific transitions
     - Component animation presets
  
  Usage Guidelines:
  - Always use tokens instead of hard-coded values
  - Follow the naming convention: --{category}-{property}-{variant}
  - Use semantic tokens when available (e.g., --color-text-primary)
  - Maintain consistency across components
  
  Extending the System:
  - Add new tokens to the appropriate category file
  - Update legacy mappings when refactoring existing code
  - Document any new token categories or major changes
*/