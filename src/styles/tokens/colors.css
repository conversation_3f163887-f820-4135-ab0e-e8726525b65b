:root {
  /* Core Brand Colors */
  --color-brand-primary: #0E639C;
  --color-brand-primary-light: #1e73ac;
  --color-brand-primary-dark: #0e538c;
  --color-brand-primary-alpha-10: rgba(14, 99, 156, 0.1);
  --color-brand-primary-alpha-20: rgba(14, 99, 156, 0.2);
  --color-brand-primary-alpha-40: rgba(14, 99, 156, 0.4);

  /* Semantic Colors */
  --color-success: #1e8449;
  --color-success-light: #2ecc71;
  --color-success-dark: #0e7439;
  --color-success-alpha-20: rgba(30, 132, 73, 0.2);

  --color-warning: #f39c12;
  --color-warning-light: #f4ac22;
  --color-warning-dark: #e38c02;
  --color-warning-alpha-20: rgba(243, 156, 18, 0.2);

  --color-error: #e74c3c;
  --color-error-light: #f75c4c;
  --color-error-dark: #d73c2c;
  --color-error-alpha-20: rgba(231, 76, 60, 0.2);

  --color-info: #3498db;
  --color-info-light: #44a8eb;
  --color-info-dark: #2488cb;
  --color-info-alpha-20: rgba(52, 152, 219, 0.2);

  /* Neutral Colors - Light Theme */
  --color-neutral-0: #ffffff;
  --color-neutral-50: #f8f8f8;
  --color-neutral-100: #f0f0f0;
  --color-neutral-200: #e6e6e6;
  --color-neutral-300: #dedede;
  --color-neutral-400: #d3d3d3;
  --color-neutral-500: #c0c0c0;
  --color-neutral-600: #aaaaaa;
  --color-neutral-700: #808080;
  --color-neutral-800: #666666;
  --color-neutral-900: #333333;
  --color-neutral-1000: #1a1a1a;

  /* Background Colors */
  --color-bg-primary: var(--color-neutral-200);
  --color-bg-secondary: var(--color-neutral-300);
  --color-bg-tertiary: var(--color-neutral-100);
  --color-bg-elevated: var(--color-neutral-0);
  --color-bg-overlay: rgba(0, 0, 0, 0.5);
  --color-bg-selected: rgba(0, 0, 0, 0.08);
  --color-bg-hover: var(--color-neutral-400);
  --color-bg-active: rgba(14, 99, 156, 0.15);

  /* Text Colors */
  --color-text-primary: var(--color-neutral-900);
  --color-text-secondary: var(--color-neutral-800);
  --color-text-tertiary: var(--color-neutral-700);
  --color-text-disabled: var(--color-neutral-600);
  --color-text-placeholder: var(--color-neutral-600);
  --color-text-inverse: var(--color-neutral-0);
  --color-text-link: var(--color-brand-primary);
  --color-text-link-hover: var(--color-brand-primary-dark);

  /* Border Colors */
  --color-border-default: var(--color-neutral-500);
  --color-border-light: var(--color-neutral-400);
  --color-border-dark: var(--color-neutral-700);
  --color-border-focus: var(--color-brand-primary);
  --color-border-error: var(--color-error);
  --color-border-success: var(--color-success);

  /* Icon Colors */
  --color-icon-default: #555555;
  --color-icon-secondary: var(--color-neutral-700);
  --color-icon-disabled: var(--color-neutral-600);
  --color-icon-inverse: var(--color-neutral-0);
  --color-icon-brand: var(--color-brand-primary);

  /* Component-Specific Colors */
  --color-button-primary-bg: #6d6d6d;
  --color-button-primary-text: var(--color-neutral-0);
  --color-button-primary-hover: #5d5d5d;
  --color-button-primary-active: #4d4d4d;

  --color-scrollbar-track: #cecece;
  --color-scrollbar-thumb: var(--color-neutral-600);
  --color-scrollbar-thumb-hover: #888888;

  /* Glass Morphism */
  --color-glass-bg: rgba(245, 245, 247, 0.85);
  --color-glass-border: rgba(255, 255, 255, 0.18);
  --color-glass-shadow: rgba(0, 0, 0, 0.12);
}

.dark-mode {
  /* Neutral Colors - Dark Theme */
  --color-neutral-0: #0a0a0a;
  --color-neutral-50: #141414;
  --color-neutral-100: #1E1E1E;
  --color-neutral-200: #252526;
  --color-neutral-300: #2A2D2E;
  --color-neutral-400: #333333;
  --color-neutral-500: #3E3E42;
  --color-neutral-600: #505050;
  --color-neutral-700: #666666;
  --color-neutral-800: #808080;
  --color-neutral-900: #BBBBBB;
  --color-neutral-1000: #E8E8E8;

  /* Background Colors */
  --color-bg-primary: var(--color-neutral-100);
  --color-bg-secondary: var(--color-neutral-200);
  --color-bg-tertiary: var(--color-neutral-300);
  --color-bg-elevated: var(--color-neutral-50);
  --color-bg-overlay: rgba(0, 0, 0, 0.7);
  --color-bg-selected: var(--color-neutral-300);
  --color-bg-hover: var(--color-neutral-400);
  --color-bg-active: rgba(14, 99, 156, 0.25);

  /* Text Colors */
  --color-text-primary: var(--color-neutral-1000);
  --color-text-secondary: var(--color-neutral-900);
  --color-text-tertiary: var(--color-neutral-800);
  --color-text-disabled: var(--color-neutral-700);
  --color-text-placeholder: var(--color-neutral-700);
  --color-text-inverse: var(--color-neutral-100);
  --color-text-link: var(--color-brand-primary-light);
  --color-text-link-hover: var(--color-brand-primary);

  /* Border Colors */
  --color-border-default: var(--color-neutral-500);
  --color-border-light: var(--color-neutral-400);
  --color-border-dark: var(--color-neutral-600);
  --color-border-focus: var(--color-brand-primary);
  --color-border-error: var(--color-error);
  --color-border-success: var(--color-success-light);

  /* Icon Colors */
  --color-icon-default: #CCCCCC;
  --color-icon-secondary: var(--color-neutral-800);
  --color-icon-disabled: var(--color-neutral-700);
  --color-icon-inverse: var(--color-neutral-100);
  --color-icon-brand: var(--color-brand-primary-light);

  /* Component-Specific Colors */
  --color-button-primary-bg: var(--color-brand-primary);
  --color-button-primary-text: var(--color-neutral-0);
  --color-button-primary-hover: var(--color-brand-primary-light);
  --color-button-primary-active: var(--color-brand-primary-dark);

  --color-scrollbar-track: var(--color-neutral-100);
  --color-scrollbar-thumb: var(--color-neutral-500);
  --color-scrollbar-thumb-hover: var(--color-neutral-600);

  /* Glass Morphism */
  --color-glass-bg: rgba(40, 40, 40, 0.85);
  --color-glass-border: rgba(255, 255, 255, 0.08);
  --color-glass-shadow: rgba(0, 0, 0, 0.4);
}

/* Legacy Mappings (for backward compatibility) */
:root {
  --background-primary: var(--color-bg-primary);
  --background-secondary: var(--color-bg-secondary);
  --background-selected: var(--color-bg-selected);
  --accent-blue: var(--color-brand-primary);
  --border-color: var(--color-border-default);
  --hover-color: var(--color-bg-hover);
  --text-primary: var(--color-text-primary);
  --text-secondary: var(--color-text-secondary);
  --text-disabled: var(--color-text-disabled);
  --icon-color: var(--color-icon-default);
  --success-color: var(--color-success);
  --warning-color: var(--color-warning);
  --error-color: var(--color-error);
  --primary-button-background: var(--color-button-primary-bg);
  --primary-button-text: var(--color-button-primary-text);
  --file-card-selected-border: var(--color-border-default);
  --checkbox-border: var(--color-border-default);
  --checkbox-background: transparent;
  --icon-stroke-color: var(--color-icon-secondary);
  --icon-hover-stroke-color: var(--color-icon-default);
  --scrollbar-track: var(--color-scrollbar-track);
  --scrollbar-thumb: var(--color-scrollbar-thumb);
  --scrollbar-thumb-hover: var(--color-scrollbar-thumb-hover);
  --option-selected-bg: var(--color-neutral-900);
  --option-selected-text: var(--color-neutral-0);
}