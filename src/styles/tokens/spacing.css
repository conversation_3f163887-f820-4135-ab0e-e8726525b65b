:root {
  /* Base Spacing Unit (8px grid system) */
  --spacing-unit: 8px;

  /* Spacing Scale */
  --spacing-0: 0;
  --spacing-1: 4px;   /* 0.5 * unit */
  --spacing-2: 8px;   /* 1 * unit */
  --spacing-3: 12px;  /* 1.5 * unit */
  --spacing-4: 16px;  /* 2 * unit */
  --spacing-5: 20px;  /* 2.5 * unit */
  --spacing-6: 24px;  /* 3 * unit */
  --spacing-7: 28px;  /* 3.5 * unit */
  --spacing-8: 32px;  /* 4 * unit */
  --spacing-9: 36px;  /* 4.5 * unit */
  --spacing-10: 40px; /* 5 * unit */
  --spacing-12: 48px; /* 6 * unit */
  --spacing-14: 56px; /* 7 * unit */
  --spacing-16: 64px; /* 8 * unit */
  --spacing-20: 80px; /* 10 * unit */
  --spacing-24: 96px; /* 12 * unit */
  --spacing-32: 128px; /* 16 * unit */

  /* Semantic Spacing */
  --spacing-xs: var(--spacing-1);  /* 4px */
  --spacing-sm: var(--spacing-2);  /* 8px */
  --spacing-md: var(--spacing-4);  /* 16px */
  --spacing-lg: var(--spacing-6);  /* 24px */
  --spacing-xl: var(--spacing-8);  /* 32px */
  --spacing-2xl: var(--spacing-12); /* 48px */
  --spacing-3xl: var(--spacing-16); /* 64px */

  /* Component Spacing */
  --spacing-inline-xs: var(--spacing-1);
  --spacing-inline-sm: var(--spacing-2);
  --spacing-inline-md: var(--spacing-3);
  --spacing-inline-lg: var(--spacing-4);

  --spacing-stack-xs: var(--spacing-1);
  --spacing-stack-sm: var(--spacing-2);
  --spacing-stack-md: var(--spacing-4);
  --spacing-stack-lg: var(--spacing-6);
  --spacing-stack-xl: var(--spacing-8);

  /* Container Padding */
  --spacing-container-xs: var(--spacing-2);
  --spacing-container-sm: var(--spacing-3);
  --spacing-container-md: var(--spacing-4);
  --spacing-container-lg: var(--spacing-6);
  --spacing-container-xl: var(--spacing-8);

  /* Gap Spacing (for flexbox/grid) */
  --gap-xs: var(--spacing-1);
  --gap-sm: var(--spacing-2);
  --gap-md: var(--spacing-3);
  --gap-lg: var(--spacing-4);
  --gap-xl: var(--spacing-6);

  /* Layout Spacing */
  --layout-header-height: 60px;
  --layout-sidebar-width: 300px;
  --layout-sidebar-collapsed-width: 60px;
  --layout-content-max-width: 1200px;
  --layout-modal-max-width: 600px;
  --layout-modal-padding: var(--spacing-6);

  /* Workspace Specific */
  --workspace-item-height: 44px;
  --workspace-item-padding: var(--spacing-3);
  --workspace-dropdown-min-width: 200px;
  --workspace-dropdown-max-height: 400px;

  /* File Card Spacing */
  --file-card-padding: var(--spacing-3);
  --file-card-margin: var(--spacing-2);
  --file-card-icon-gap: var(--spacing-2);

  /* Button Spacing */
  --button-padding-x-sm: var(--spacing-2);
  --button-padding-y-sm: var(--spacing-1);
  --button-padding-x-md: var(--spacing-3);
  --button-padding-y-md: var(--spacing-2);
  --button-padding-x-lg: var(--spacing-4);
  --button-padding-y-lg: var(--spacing-3);
  --button-icon-gap: var(--spacing-2);

  /* Input Spacing */
  --input-padding-x: var(--spacing-3);
  --input-padding-y: var(--spacing-2);
  --input-label-gap: var(--spacing-1);

  /* Modal Spacing */
  --modal-padding: var(--spacing-6);
  --modal-header-spacing: var(--spacing-4);
  --modal-body-spacing: var(--spacing-4);
  --modal-footer-spacing: var(--spacing-4);

  /* Card Spacing */
  --card-padding: var(--spacing-4);
  --card-header-spacing: var(--spacing-3);
  --card-body-spacing: var(--spacing-3);
  --card-footer-spacing: var(--spacing-3);

  /* List Spacing */
  --list-item-padding-x: var(--spacing-3);
  --list-item-padding-y: var(--spacing-2);
  --list-item-gap: var(--spacing-1);

  /* Tree View Spacing */
  --tree-indent: var(--spacing-4);
  --tree-item-padding: var(--spacing-1);
  --tree-item-height: 28px;

  /* Dropdown Spacing */
  --dropdown-padding: var(--spacing-1);
  --dropdown-item-padding-x: var(--spacing-3);
  --dropdown-item-padding-y: var(--spacing-2);
  --dropdown-max-height: 320px;

  /* Icon Spacing */
  --icon-size-xs: 12px;
  --icon-size-sm: 16px;
  --icon-size-md: 20px;
  --icon-size-lg: 24px;
  --icon-size-xl: 32px;

  /* Focus Outline Offset */
  --focus-outline-offset: 2px;
  --focus-outline-width: 2px;
}