:root {
  /* Border Widths */
  --border-width-0: 0;
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-3: 3px;
  --border-width-4: 4px;
  --border-width-thick: 5px;

  /* Semantic Border Widths */
  --border-width-thin: var(--border-width-1);
  --border-width-base: var(--border-width-1);
  --border-width-medium: var(--border-width-2);
  --border-width-bold: var(--border-width-3);

  /* Border Radius */
  --radius-none: 0;
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;
  --radius-full: 9999px;
  --radius-circle: 50%;

  /* Component Border Radius */
  --radius-button: var(--radius-md);
  --radius-input: var(--radius-sm);
  --radius-card: var(--radius-lg);
  --radius-modal: var(--radius-xl);
  --radius-dropdown: var(--radius-lg);
  --radius-tooltip: var(--radius-sm);
  --radius-badge: var(--radius-full);
  --radius-checkbox: var(--radius-xs);
  --radius-file-card: var(--radius-md);

  /* Border Styles */
  --border-style-none: none;
  --border-style-solid: solid;
  --border-style-dashed: dashed;
  --border-style-dotted: dotted;
  --border-style-double: double;

  /* Focus Ring */
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
  --focus-ring-color: var(--color-brand-primary, #0E639C);
  --focus-ring-style: solid;
  --focus-ring: 0 0 0 var(--focus-ring-width) var(--focus-ring-color);
  --focus-ring-inset: inset 0 0 0 var(--focus-ring-width) var(--focus-ring-color);

  /* Component Borders */
  --border-default: var(--border-width-base) var(--border-style-solid) var(--color-border-default, #c0c0c0);
  --border-light: var(--border-width-base) var(--border-style-solid) var(--color-border-light, #d3d3d3);
  --border-dark: var(--border-width-base) var(--border-style-solid) var(--color-border-dark, #808080);
  --border-focus: var(--border-width-base) var(--border-style-solid) var(--color-border-focus, #0E639C);
  --border-error: var(--border-width-base) var(--border-style-solid) var(--color-border-error, #e74c3c);
  --border-success: var(--border-width-base) var(--border-style-solid) var(--color-border-success, #1e8449);
  --border-warning: var(--border-width-base) var(--border-style-solid) var(--color-border-warning, #f39c12);

  /* Dividers */
  --divider-horizontal: var(--border-width-base) var(--border-style-solid) var(--color-border-light, #d3d3d3);
  --divider-vertical: var(--border-width-base) var(--border-style-solid) var(--color-border-light, #d3d3d3);
  --divider-dashed: var(--border-width-base) var(--border-style-dashed) var(--color-border-light, #d3d3d3);

  /* Outline Styles */
  --outline-none: none;
  --outline-default: var(--border-width-base) var(--border-style-solid) var(--color-border-default, #c0c0c0);
  --outline-focus: var(--focus-ring-width) var(--border-style-solid) var(--focus-ring-color);
  --outline-offset: var(--focus-ring-offset);

  /* Border Shortcuts for Components */
  --input-border: var(--border-default);
  --input-border-focus: var(--border-focus);
  --input-border-error: var(--border-error);
  --input-border-radius: var(--radius-input);

  --button-border: var(--border-width-base) var(--border-style-solid) transparent;
  --button-border-radius: var(--radius-button);

  --card-border: var(--border-light);
  --card-border-radius: var(--radius-card);

  --modal-border: var(--border-width-0) var(--border-style-none) transparent;
  --modal-border-radius: var(--radius-modal);

  --dropdown-border: var(--border-light);
  --dropdown-border-radius: var(--radius-dropdown);
  --dropdown-item-border-radius: var(--radius-sm);

  --file-card-border: var(--border-default);
  --file-card-border-selected: var(--border-width-2) var(--border-style-solid) var(--color-border-focus, #0E639C);
  --file-card-border-radius: var(--radius-file-card);

  /* Table Borders */
  --table-border: var(--border-default);
  --table-border-header: var(--border-dark);
  --table-border-cell: var(--border-light);

  /* Decorative Borders */
  --border-gradient: linear-gradient(to right, transparent, var(--color-border-default, #c0c0c0), transparent);
  --border-gradient-vertical: linear-gradient(to bottom, transparent, var(--color-border-default, #c0c0c0), transparent);
}