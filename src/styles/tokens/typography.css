:root {
  /* Font Families */
  --font-family-primary: '<PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  --font-family-secondary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'Consolas', 'Courier New', monospace;

  /* Font Sizes */
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  --font-size-5xl: 48px;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* Text Transform */
  --text-transform-none: none;
  --text-transform-uppercase: uppercase;
  --text-transform-lowercase: lowercase;
  --text-transform-capitalize: capitalize;

  /* Component Typography */
  
  /* Headings */
  --heading-1-size: var(--font-size-3xl);
  --heading-1-weight: var(--font-weight-bold);
  --heading-1-line-height: var(--line-height-tight);
  --heading-1-letter-spacing: var(--letter-spacing-tight);

  --heading-2-size: var(--font-size-2xl);
  --heading-2-weight: var(--font-weight-semibold);
  --heading-2-line-height: var(--line-height-tight);
  --heading-2-letter-spacing: var(--letter-spacing-tight);

  --heading-3-size: var(--font-size-xl);
  --heading-3-weight: var(--font-weight-semibold);
  --heading-3-line-height: var(--line-height-snug);
  --heading-3-letter-spacing: var(--letter-spacing-normal);

  --heading-4-size: var(--font-size-lg);
  --heading-4-weight: var(--font-weight-medium);
  --heading-4-line-height: var(--line-height-snug);
  --heading-4-letter-spacing: var(--letter-spacing-normal);

  --heading-5-size: var(--font-size-md);
  --heading-5-weight: var(--font-weight-medium);
  --heading-5-line-height: var(--line-height-normal);
  --heading-5-letter-spacing: var(--letter-spacing-normal);

  --heading-6-size: var(--font-size-base);
  --heading-6-weight: var(--font-weight-semibold);
  --heading-6-line-height: var(--line-height-normal);
  --heading-6-letter-spacing: var(--letter-spacing-wide);

  /* Body Text */
  --body-size: var(--font-size-base);
  --body-weight: var(--font-weight-regular);
  --body-line-height: var(--line-height-normal);
  --body-letter-spacing: var(--letter-spacing-normal);

  --body-small-size: var(--font-size-sm);
  --body-small-weight: var(--font-weight-regular);
  --body-small-line-height: var(--line-height-normal);
  --body-small-letter-spacing: var(--letter-spacing-normal);

  --body-large-size: var(--font-size-md);
  --body-large-weight: var(--font-weight-regular);
  --body-large-line-height: var(--line-height-relaxed);
  --body-large-letter-spacing: var(--letter-spacing-normal);

  /* Button Text */
  --button-text-size-sm: var(--font-size-xs);
  --button-text-size-md: var(--font-size-sm);
  --button-text-size-lg: var(--font-size-base);
  --button-text-weight: var(--font-weight-medium);
  --button-text-letter-spacing: var(--letter-spacing-wide);
  --button-text-transform: var(--text-transform-none);

  /* Input Text */
  --input-text-size: var(--font-size-base);
  --input-text-weight: var(--font-weight-regular);
  --input-label-size: var(--font-size-sm);
  --input-label-weight: var(--font-weight-medium);
  --input-placeholder-weight: var(--font-weight-light);

  /* Caption Text */
  --caption-size: var(--font-size-xs);
  --caption-weight: var(--font-weight-regular);
  --caption-line-height: var(--line-height-normal);
  --caption-letter-spacing: var(--letter-spacing-wide);

  /* Code Text */
  --code-size: var(--font-size-sm);
  --code-weight: var(--font-weight-regular);
  --code-line-height: var(--line-height-normal);
  --code-family: var(--font-family-mono);

  /* Dropdown Typography (Legacy Support) */
  --dropdown-font-size-xs: var(--font-size-xs);
  --dropdown-font-size-sm: var(--font-size-sm);
  --dropdown-font-size-base: var(--font-size-sm);
  --dropdown-font-size-md: var(--font-size-base);
  
  --dropdown-font-light: var(--font-weight-light);
  --dropdown-font-regular: var(--font-weight-regular);
  --dropdown-font-medium: var(--font-weight-medium);
  
  --dropdown-line-height-tight: var(--line-height-tight);
  --dropdown-line-height-base: var(--line-height-normal);
  --dropdown-line-height-relaxed: var(--line-height-relaxed);
  
  --dropdown-letter-spacing-tight: var(--letter-spacing-tight);
  --dropdown-letter-spacing-base: var(--letter-spacing-wide);
  --dropdown-letter-spacing-wide: var(--letter-spacing-wider);

  /* Text Decoration */
  --text-decoration-none: none;
  --text-decoration-underline: underline;
  --text-decoration-line-through: line-through;

  /* Text Alignment */
  --text-align-left: left;
  --text-align-center: center;
  --text-align-right: right;
  --text-align-justify: justify;

  /* Max Line Length (for readability) */
  --max-line-length: 65ch;
  --max-line-length-narrow: 45ch;
  --max-line-length-wide: 80ch;
}