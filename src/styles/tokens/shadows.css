:root {
  /* Shadow Colors */
  --shadow-color-light: rgba(0, 0, 0, 0.1);
  --shadow-color-medium: rgba(0, 0, 0, 0.15);
  --shadow-color-dark: rgba(0, 0, 0, 0.2);
  --shadow-color-brand: rgba(14, 99, 156, 0.2);

  /* Elevation System (Material Design inspired) */
  --shadow-0: none;
  --shadow-1: 0 1px 3px 0 var(--shadow-color-light), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-2: 0 4px 6px -1px var(--shadow-color-light), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-3: 0 10px 15px -3px var(--shadow-color-light), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-4: 0 20px 25px -5px var(--shadow-color-light), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-5: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Semantic Shadows */
  --shadow-xs: 0 0 0 1px rgba(0, 0, 0, 0.05);
  --shadow-sm: var(--shadow-1);
  --shadow-md: var(--shadow-2);
  --shadow-lg: var(--shadow-3);
  --shadow-xl: var(--shadow-4);
  --shadow-2xl: var(--shadow-5);

  /* Directional Shadows */
  --shadow-up: 0 -1px 3px 0 var(--shadow-color-light);
  --shadow-down: 0 1px 3px 0 var(--shadow-color-light);
  --shadow-left: -1px 0 3px 0 var(--shadow-color-light);
  --shadow-right: 1px 0 3px 0 var(--shadow-color-light);

  /* Inset Shadows */
  --shadow-inner-xs: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
  --shadow-inner-sm: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-inner-md: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-inner-lg: inset 0 4px 6px -1px rgba(0, 0, 0, 0.1);

  /* Component Shadows */
  --shadow-button: var(--shadow-sm);
  --shadow-button-hover: var(--shadow-md);
  --shadow-button-active: var(--shadow-xs);

  --shadow-card: var(--shadow-md);
  --shadow-card-hover: var(--shadow-lg);

  --shadow-modal: var(--shadow-2xl);
  --shadow-modal-backdrop: rgba(0, 0, 0, 0.5);

  --shadow-dropdown: var(--shadow-lg);
  --shadow-tooltip: var(--shadow-md);

  --shadow-input: var(--shadow-inner-sm);
  --shadow-input-focus: 0 0 0 3px var(--shadow-color-brand);

  --shadow-file-card: var(--shadow-sm);
  --shadow-file-card-hover: var(--shadow-md);
  --shadow-file-card-selected: 0 0 0 2px var(--shadow-color-brand);

  /* Glass Morphism Shadows */
  --shadow-glass: 0 8px 32px var(--shadow-color-light);
  --shadow-glass-hover: 0 12px 48px var(--shadow-color-medium);
  --shadow-glass-active: 0 4px 16px var(--shadow-color-light);

  /* Dropdown Glass Shadows (Legacy Support) */
  --dropdown-glass-shadow: var(--shadow-glass);
  --dropdown-focus-glow: 0 0 0 3px var(--shadow-color-brand);

  /* Text Shadows */
  --text-shadow-sm: 0 1px 2px var(--shadow-color-light);
  --text-shadow-md: 0 2px 4px var(--shadow-color-medium);
  --text-shadow-lg: 0 4px 8px var(--shadow-color-dark);

  /* Glow Effects */
  --glow-sm: 0 0 10px var(--shadow-color-brand);
  --glow-md: 0 0 20px var(--shadow-color-brand);
  --glow-lg: 0 0 30px var(--shadow-color-brand);

  /* Scrollbar Shadow */
  --shadow-scrollbar: inset 0 0 6px rgba(0, 0, 0, 0.1);

  /* Workspace Drag Shadow */
  --shadow-drag: 0 5px 15px rgba(0, 0, 0, 0.3);
  --shadow-drag-placeholder: inset 0 0 0 2px var(--shadow-color-brand);
}

.dark-mode {
  /* Shadow Colors - Dark Mode */
  --shadow-color-light: rgba(0, 0, 0, 0.3);
  --shadow-color-medium: rgba(0, 0, 0, 0.4);
  --shadow-color-dark: rgba(0, 0, 0, 0.5);
  --shadow-color-brand: rgba(14, 99, 156, 0.3);

  /* Enhanced shadows for dark mode */
  --shadow-1: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-2: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-3: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
  --shadow-4: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.14);
  --shadow-5: 0 25px 50px -12px rgba(0, 0, 0, 0.5);

  /* Glass Morphism Shadows - Dark Mode */
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.4);
  --shadow-glass-hover: 0 12px 48px rgba(0, 0, 0, 0.5);
  --shadow-glass-active: 0 4px 16px rgba(0, 0, 0, 0.3);

  /* Modal backdrop darker in dark mode */
  --shadow-modal-backdrop: rgba(0, 0, 0, 0.7);

  /* Glow effects adjusted for dark mode */
  --glow-sm: 0 0 10px rgba(14, 99, 156, 0.5);
  --glow-md: 0 0 20px rgba(14, 99, 156, 0.5);
  --glow-lg: 0 0 30px rgba(14, 99, 156, 0.5);
}