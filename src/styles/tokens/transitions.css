:root {
  /* Transition Durations */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-base: 200ms;
  --duration-moderate: 300ms;
  --duration-slow: 400ms;
  --duration-slower: 500ms;
  --duration-slowest: 600ms;

  /* Semantic Durations */
  --duration-enter: var(--duration-base);
  --duration-exit: var(--duration-fast);
  --duration-hover: var(--duration-fast);
  --duration-focus: var(--duration-instant);
  --duration-collapse: var(--duration-moderate);
  --duration-expand: var(--duration-moderate);
  --duration-fade: var(--duration-base);
  --duration-slide: var(--duration-moderate);
  --duration-modal: var(--duration-moderate);

  /* Easing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Material Design Easing */
  --ease-standard: cubic-bezier(0.4, 0, 0.2, 1);        /* Standard curve */
  --ease-decelerate: cubic-bezier(0, 0, 0.2, 1);        /* Deceleration curve */
  --ease-accelerate: cubic-bezier(0.4, 0, 1, 1);        /* Acceleration curve */
  --ease-sharp: cubic-bezier(0.4, 0, 0.6, 1);           /* Sharp curve */

  /* Custom Easing */
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.68, -0.6, 0.32, 1.6);
  --ease-back-in: cubic-bezier(0.6, -0.28, 0.735, 0.045);
  --ease-back-out: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-back-in-out: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Semantic Easing */
  --ease-default: var(--ease-standard);
  --ease-enter: var(--ease-decelerate);
  --ease-exit: var(--ease-accelerate);
  --ease-move: var(--ease-standard);
  --ease-resize: var(--ease-standard);

  /* Component Transitions */
  --transition-all: all var(--duration-base) var(--ease-default);
  --transition-none: none;

  /* Property-specific Transitions */
  --transition-colors: 
    color var(--duration-fast) var(--ease-default),
    background-color var(--duration-fast) var(--ease-default),
    border-color var(--duration-fast) var(--ease-default),
    fill var(--duration-fast) var(--ease-default),
    stroke var(--duration-fast) var(--ease-default);

  --transition-opacity: opacity var(--duration-base) var(--ease-default);
  --transition-transform: transform var(--duration-base) var(--ease-default);
  --transition-shadow: box-shadow var(--duration-base) var(--ease-default);
  --transition-border: border var(--duration-fast) var(--ease-default);

  /* Combined Transitions */
  --transition-fade: 
    opacity var(--duration-fade) var(--ease-default),
    visibility var(--duration-fade) var(--ease-default);

  --transition-slide: 
    transform var(--duration-slide) var(--ease-decelerate),
    opacity var(--duration-slide) var(--ease-decelerate);

  --transition-scale: 
    transform var(--duration-base) var(--ease-bounce),
    opacity var(--duration-base) var(--ease-default);

  /* Component-specific Transitions */
  --transition-button: 
    background-color var(--duration-fast) var(--ease-default),
    color var(--duration-fast) var(--ease-default),
    border-color var(--duration-fast) var(--ease-default),
    box-shadow var(--duration-fast) var(--ease-default),
    transform var(--duration-fast) var(--ease-bounce);

  --transition-input: 
    border-color var(--duration-fast) var(--ease-default),
    box-shadow var(--duration-fast) var(--ease-default),
    background-color var(--duration-fast) var(--ease-default);

  --transition-card: 
    box-shadow var(--duration-base) var(--ease-default),
    transform var(--duration-base) var(--ease-default),
    background-color var(--duration-base) var(--ease-default);

  --transition-modal: 
    opacity var(--duration-modal) var(--ease-decelerate),
    transform var(--duration-modal) var(--ease-decelerate);

  --transition-dropdown: 
    opacity var(--duration-fast) var(--ease-decelerate),
    transform var(--duration-fast) var(--ease-decelerate),
    visibility var(--duration-fast) var(--ease-decelerate);

  --transition-file-card: 
    background-color var(--duration-fast) var(--ease-default),
    border-color var(--duration-fast) var(--ease-default),
    box-shadow var(--duration-fast) var(--ease-default),
    transform var(--duration-fast) var(--ease-default);

  --transition-tree-item: 
    background-color var(--duration-fast) var(--ease-default),
    padding-left var(--duration-base) var(--ease-default);

  --transition-collapse: 
    max-height var(--duration-collapse) var(--ease-decelerate),
    opacity var(--duration-collapse) var(--ease-decelerate),
    transform var(--duration-collapse) var(--ease-decelerate);

  /* Workspace Transitions (Legacy Support) */
  --workspace-drag-transition: var(--duration-base);
  --workspace-drag-opacity: 0.5;

  /* Dropdown Transitions (Legacy Support) */
  --dropdown-easing-smooth: var(--ease-standard);
  --dropdown-easing-bounce: var(--ease-bounce);
  --dropdown-duration-fast: var(--duration-fast);
  --dropdown-duration-base: var(--duration-base);
  --dropdown-duration-slow: var(--duration-moderate);

  /* Animation Keyframes Timing */
  --animation-duration-pulse: 2s;
  --animation-duration-spin: 1s;
  --animation-duration-ping: 1s;
  --animation-duration-bounce: 1s;
  --animation-duration-fade-in: var(--duration-base);
  --animation-duration-fade-out: var(--duration-fast);
  --animation-duration-slide-in: var(--duration-moderate);
  --animation-duration-slide-out: var(--duration-base);

  /* Delays */
  --delay-none: 0ms;
  --delay-short: 100ms;
  --delay-medium: 200ms;
  --delay-long: 300ms;
  --delay-tooltip: 500ms;
  --delay-hover-intent: 150ms;
}