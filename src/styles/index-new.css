/* PasteFlow CSS Architecture - Main Entry Point */
/* This file orchestrates the loading of all CSS modules in the correct order */

/* 1. Design Tokens - Foundation of the design system */
@import './tokens/index.css';

/* 2. Base Layer - Resets and foundational styles */
@import './base/index.css';

/* 3. Critical Styles - Above-the-fold essentials (can be inlined for performance) */
@import './critical.css';

/* 4. Layout Components - Major structural elements */
/* These will be populated as we migrate from the monolithic index.css */
/* @import './layout/index.css'; */

/* 5. Component Styles - UI components and patterns */
/* These will be populated as we migrate from the monolithic index.css */
/* @import './components/index.css'; */

/* 6. Utility Classes - Helper classes for common patterns */
/* These will be populated as we migrate from the monolithic index.css */
/* @import './utilities/index.css'; */

/* 7. Non-Critical Styles - Below-the-fold enhancements */
/* Consider loading this asynchronously in production */
@import './non-critical.css';

/* 8. Legacy Imports - To be refactored */
@import './radix-modal.css';
@import './fonts.css';

/* 
  Migration Notes:
  - The original index.css is 3087 lines and needs to be broken down
  - Components should be extracted to ./components/ directory
  - Layout styles should be extracted to ./layout/ directory
  - Utility classes should be extracted to ./utilities/ directory
  - Each component should have its own CSS file
  - Use CSS custom properties from the token system
  
  Performance Optimization:
  - Consider inlining critical.css in the HTML head
  - Load non-critical.css asynchronously
  - Use PostCSS to optimize and minify in production
  - Enable CSS code splitting if using CSS modules
  
  Next Steps:
  1. Extract component styles from original index.css
  2. Create individual component CSS files
  3. Update React components to use modular CSS
  4. Remove duplicate and unused styles
  5. Optimize selector specificity
*/