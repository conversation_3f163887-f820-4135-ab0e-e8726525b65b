/* Base Typography Styles */

/* Root font settings */
body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
  color: var(--color-text-primary);
}

/* Headings */
h1 {
  font-size: var(--heading-1-size);
  font-weight: var(--heading-1-weight);
  line-height: var(--heading-1-line-height);
  letter-spacing: var(--heading-1-letter-spacing);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

h2 {
  font-size: var(--heading-2-size);
  font-weight: var(--heading-2-weight);
  line-height: var(--heading-2-line-height);
  letter-spacing: var(--heading-2-letter-spacing);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
}

h3 {
  font-size: var(--heading-3-size);
  font-weight: var(--heading-3-weight);
  line-height: var(--heading-3-line-height);
  letter-spacing: var(--heading-3-letter-spacing);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
}

h4 {
  font-size: var(--heading-4-size);
  font-weight: var(--heading-4-weight);
  line-height: var(--heading-4-line-height);
  letter-spacing: var(--heading-4-letter-spacing);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

h5 {
  font-size: var(--heading-5-size);
  font-weight: var(--heading-5-weight);
  line-height: var(--heading-5-line-height);
  letter-spacing: var(--heading-5-letter-spacing);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

h6 {
  font-size: var(--heading-6-size);
  font-weight: var(--heading-6-weight);
  line-height: var(--heading-6-line-height);
  letter-spacing: var(--heading-6-letter-spacing);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

/* Body text */
p {
  font-size: var(--body-size);
  font-weight: var(--body-weight);
  line-height: var(--body-line-height);
  letter-spacing: var(--body-letter-spacing);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-3);
}

p:last-child {
  margin-bottom: 0;
}

/* Lead paragraph */
.lead {
  font-size: var(--body-large-size);
  font-weight: var(--body-large-weight);
  line-height: var(--body-large-line-height);
  letter-spacing: var(--body-large-letter-spacing);
  color: var(--color-text-primary);
}

/* Small text */
small,
.small {
  font-size: var(--body-small-size);
  font-weight: var(--body-small-weight);
  line-height: var(--body-small-line-height);
  letter-spacing: var(--body-small-letter-spacing);
}

/* Strong and bold */
strong,
b {
  font-weight: var(--font-weight-bold);
}

/* Emphasis */
em,
i {
  font-style: italic;
}

/* Links */
a {
  color: var(--color-text-link);
  text-decoration: none;
  transition: var(--transition-colors);
}

a:hover {
  color: var(--color-text-link-hover);
  text-decoration: underline;
}

a:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
  border-radius: var(--radius-xs);
}

/* Lists */
ul,
ol {
  margin-bottom: var(--spacing-3);
  padding-left: var(--spacing-6);
}

ul ul,
ul ol,
ol ul,
ol ol {
  margin-bottom: 0;
}

li {
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-relaxed);
}

/* Description lists */
dl {
  margin-bottom: var(--spacing-3);
}

dt {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

dd {
  margin-left: var(--spacing-4);
  margin-bottom: var(--spacing-2);
  color: var(--color-text-secondary);
}

/* Blockquotes */
blockquote {
  margin: var(--spacing-4) 0;
  padding: var(--spacing-3) var(--spacing-4);
  border-left: 4px solid var(--color-brand-primary);
  background-color: var(--color-bg-tertiary);
  font-style: italic;
  color: var(--color-text-secondary);
}

blockquote p:last-child {
  margin-bottom: 0;
}

blockquote cite {
  display: block;
  margin-top: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-style: normal;
  color: var(--color-text-tertiary);
}

/* Code */
code {
  font-family: var(--code-family);
  font-size: var(--code-size);
  font-weight: var(--code-weight);
  line-height: var(--code-line-height);
  background-color: var(--color-bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-xs);
  color: var(--color-text-primary);
}

pre {
  font-family: var(--code-family);
  font-size: var(--code-size);
  line-height: var(--code-line-height);
  background-color: var(--color-bg-tertiary);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  overflow-x: auto;
  white-space: pre;
  margin-bottom: var(--spacing-3);
}

pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

/* Keyboard input */
kbd {
  display: inline-block;
  padding: 2px 6px;
  font-family: var(--code-family);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  color: var(--color-text-primary);
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-default);
  border-radius: var(--radius-xs);
  box-shadow: var(--shadow-xs);
}

/* Sample output */
samp {
  font-family: var(--code-family);
  font-size: var(--code-size);
}

/* Variable */
var {
  font-style: italic;
  font-weight: var(--font-weight-medium);
}

/* Abbreviation */
abbr[title] {
  text-decoration: underline dotted;
  cursor: help;
}

/* Mark/highlight */
mark {
  background-color: var(--color-warning-alpha-20);
  color: var(--color-text-primary);
  padding: 2px 4px;
  border-radius: var(--radius-xs);
}

/* Deletion and insertion */
del {
  text-decoration: line-through;
  color: var(--color-text-tertiary);
}

ins {
  text-decoration: underline;
  color: var(--color-success);
}

/* Subscript and superscript */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Horizontal rule */
hr {
  border: none;
  border-top: var(--divider-horizontal);
  margin: var(--spacing-6) 0;
}

/* Text selection */
::selection {
  background-color: var(--color-brand-primary-alpha-20);
  color: var(--color-text-primary);
}

/* Responsive typography */
@media (max-width: 768px) {
  h1 {
    font-size: calc(var(--heading-1-size) * 0.85);
  }
  
  h2 {
    font-size: calc(var(--heading-2-size) * 0.85);
  }
  
  h3 {
    font-size: calc(var(--heading-3-size) * 0.9);
  }
}

/* Print styles */
@media print {
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  h1 {
    font-size: 24pt;
    page-break-after: avoid;
  }
  
  h2 {
    font-size: 18pt;
    page-break-after: avoid;
  }
  
  h3 {
    font-size: 14pt;
    page-break-after: avoid;
  }
  
  p,
  blockquote,
  ul,
  ol {
    page-break-inside: avoid;
  }
}