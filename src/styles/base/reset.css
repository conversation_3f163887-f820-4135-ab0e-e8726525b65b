/* Modern CSS Reset - Base normalization and defaults */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
}

/* Set core root defaults */
html {
  height: 100%;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  line-height: 1.5;
  position: relative;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
  color: inherit;
}

/* Make images easier to work with */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
  color: inherit;
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
    scroll-behavior: auto;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-align: inherit;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

/* Remove default blockquote and figure margins */
blockquote,
figure {
  margin: 0;
}

/* Reset heading styles */
h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

/* Reset paragraph margins */
p {
  margin: 0;
}

/* Reset list margins and paddings */
ul, ol {
  margin: 0;
  padding: 0;
}

/* Reset table styles */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Reset form elements */
input,
textarea,
select {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
}

/* Remove spinner from number inputs */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* Remove default search input styling */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

/* Reset dialog styles */
dialog {
  padding: 0;
  border: none;
  background: transparent;
}

/* Reset summary styles */
summary {
  display: list-item;
  cursor: pointer;
}

/* Ensure hidden attribute works */
[hidden] {
  display: none !important;
}

/* Reset anchor styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Focus visible only for keyboard navigation */
:focus:not(:focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
}