/* Global Element Styles and Defaults */

/* Root and HTML */
html {
  color-scheme: light;
  overflow-x: hidden;
}

html.dark-mode {
  color-scheme: dark;
}

/* Body */
body {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  overflow-x: hidden;
  position: relative;
}

/* Focus styles */
:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

/* Selection */
::selection {
  background-color: var(--color-brand-primary-alpha-20);
  color: var(--color-text-primary);
}

/* Scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar-thumb) var(--color-scrollbar-track);
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: var(--radius-full);
}

*::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar-thumb);
  border-radius: var(--radius-full);
  border: 2px solid var(--color-scrollbar-track);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-scrollbar-thumb-hover);
  border-width: 1px;
}

*::-webkit-scrollbar-corner {
  background: var(--color-scrollbar-track);
}

/* Form Elements Global Styles */
button,
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

/* Button defaults */
button {
  cursor: pointer;
  user-select: none;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Input defaults */
input,
textarea,
select {
  width: 100%;
}

input:disabled,
textarea:disabled,
select:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Placeholder styles */
::placeholder {
  color: var(--color-text-placeholder);
  opacity: 1;
}

/* File input */
input[type="file"] {
  cursor: pointer;
}

input[type="file"]::file-selector-button {
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  padding: var(--button-padding-y-md) var(--button-padding-x-md);
  border: var(--border-default);
  border-radius: var(--radius-button);
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
  transition: var(--transition-button);
}

input[type="file"]::file-selector-button:hover {
  background-color: var(--color-bg-hover);
}

/* Range input */
input[type="range"] {
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
}

input[type="range"]::-webkit-slider-track {
  background: var(--color-bg-tertiary);
  height: 4px;
  border-radius: var(--radius-full);
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-circle);
  background: var(--color-brand-primary);
  cursor: pointer;
  margin-top: -6px;
  transition: var(--transition-transform);
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

input[type="range"]::-moz-range-track {
  background: var(--color-bg-tertiary);
  height: 4px;
  border-radius: var(--radius-full);
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border: 0;
  border-radius: var(--radius-circle);
  background: var(--color-brand-primary);
  cursor: pointer;
  transition: var(--transition-transform);
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.2);
}

/* Progress element */
progress {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 8px;
  border-radius: var(--radius-full);
  overflow: hidden;
}

progress::-webkit-progress-bar {
  background-color: var(--color-bg-tertiary);
  border-radius: var(--radius-full);
}

progress::-webkit-progress-value {
  background-color: var(--color-brand-primary);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

progress::-moz-progress-bar {
  background-color: var(--color-brand-primary);
  border-radius: var(--radius-full);
}

/* Meter element */
meter {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 8px;
  border-radius: var(--radius-full);
  overflow: hidden;
}

meter::-webkit-meter-bar {
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-full);
}

meter::-webkit-meter-optimum-value {
  background: var(--color-success);
  border-radius: var(--radius-full);
}

meter::-webkit-meter-suboptimum-value {
  background: var(--color-warning);
  border-radius: var(--radius-full);
}

meter::-webkit-meter-even-less-good-value {
  background: var(--color-error);
  border-radius: var(--radius-full);
}

/* Details and Summary */
details {
  border: var(--border-default);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

summary {
  cursor: pointer;
  user-select: none;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2);
  margin: calc(var(--spacing-3) * -1);
  margin-bottom: var(--spacing-3);
  border-radius: var(--radius-md);
  transition: var(--transition-colors);
}

summary:hover {
  background-color: var(--color-bg-hover);
}

summary::-webkit-details-marker {
  color: var(--color-text-secondary);
}

details[open] summary {
  border-bottom: var(--border-default);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* Dialog */
dialog {
  padding: var(--modal-padding);
  border: var(--modal-border);
  border-radius: var(--modal-border-radius);
  background-color: var(--color-bg-elevated);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-modal);
  max-width: 90vw;
  max-height: 90vh;
}

dialog::backdrop {
  background-color: var(--shadow-modal-backdrop);
  backdrop-filter: blur(4px);
}

/* Media elements */
img,
video,
audio {
  max-width: 100%;
  height: auto;
}

img {
  display: block;
  vertical-align: middle;
}

/* SVG defaults */
svg {
  display: inline-block;
  vertical-align: middle;
  fill: currentColor;
}

/* Canvas */
canvas {
  display: block;
  vertical-align: middle;
}

/* iframe */
iframe {
  border: 0;
  display: block;
  vertical-align: middle;
}

/* Accessibility utilities */
.visually-hidden,
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.skip-to-content {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--color-bg-elevated);
  color: var(--color-text-primary);
  padding: var(--spacing-2) var(--spacing-4);
  z-index: var(--z-index-tooltip);
  text-decoration: none;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-transform);
}

.skip-to-content:focus {
  top: var(--spacing-2);
  left: var(--spacing-2);
}

/* Print styles */
@media print {
  body {
    background: white;
    color: black;
  }
  
  a {
    color: black;
    text-decoration: underline;
  }
  
  button,
  input,
  select,
  textarea {
    border: 1px solid black;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  button,
  input,
  select,
  textarea {
    border-width: 2px !important;
  }
}