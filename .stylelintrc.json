{"extends": ["stylelint-config-standard", "stylelint-config-recess-order"], "rules": {"max-nesting-depth": [3, {"ignore": ["pseudo-classes"]}], "selector-max-specificity": ["0,3,0", {"severity": "warning"}], "declaration-block-no-duplicate-properties": [true, {"ignore": ["consecutive-duplicates-with-different-values"]}], "color-function-notation": "modern", "custom-property-pattern": "^([a-z][a-z0-9]*)(-[a-z0-9]+)*$", "selector-class-pattern": "^([a-z][a-z0-9]*)(-[a-z0-9]+)*$", "selector-id-pattern": "^([a-z][a-z0-9]*)(-[a-z0-9]+)*$", "keyframes-name-pattern": "^([a-z][a-z0-9]*)(-[a-z0-9]+)*$", "font-family-no-missing-generic-family-keyword": true, "font-family-no-duplicate-names": true, "no-descending-specificity": [true, {"severity": "warning"}], "no-duplicate-selectors": true, "no-empty-source": true, "no-invalid-double-slash-comments": true, "no-irregular-whitespace": true, "property-no-unknown": [true, {"ignoreProperties": ["scrollbar-width", "scrollbar-color"]}], "unit-no-unknown": true, "function-calc-no-unspaced-operator": true, "function-linear-gradient-no-nonstandard-direction": true, "string-no-newline": true, "unit-allowed-list": ["px", "rem", "em", "%", "vh", "vw", "vmin", "vmax", "deg", "rad", "turn", "ms", "s", "fr", "ch"], "length-zero-no-unit": true, "number-max-precision": 4, "color-hex-case": "lower", "color-hex-length": "short", "color-no-invalid-hex": true, "font-weight-notation": "numeric", "function-url-quotes": "always", "comment-no-empty": true, "comment-whitespace-inside": "always", "rule-empty-line-before": ["always-multi-line", {"except": ["first-nested"], "ignore": ["after-comment"]}], "declaration-empty-line-before": "never", "block-no-empty": true, "selector-pseudo-class-no-unknown": true, "selector-pseudo-element-no-unknown": true, "selector-type-no-unknown": true, "media-feature-name-no-unknown": true, "at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen", "layer"]}], "declaration-block-no-shorthand-property-overrides": true, "property-no-vendor-prefix": [true, {"severity": "warning"}], "value-no-vendor-prefix": [true, {"severity": "warning"}], "selector-no-vendor-prefix": [true, {"severity": "warning"}], "alpha-value-notation": "number", "shorthand-property-no-redundant-values": true, "declaration-block-single-line-max-declarations": 1, "selector-max-compound-selectors": 4, "selector-max-id": 1, "selector-max-universal": 1, "selector-max-attribute": 2, "import-notation": "string", "media-feature-range-notation": "context"}, "ignoreFiles": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "dist/**", "build/**", "node_modules/**", "coverage/**", "*.min.css"]}